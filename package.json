{"name": "uifort-bo", "version": "0.0.1", "private": true, "description": "A customizable white-label back office built with Next.js.", "type": "module", "scripts": {"build-storybook": "storybook build", "build": "next build", "dev": "PORT=4000 next dev", "format:check": "prettier --config ./config/prettier.config.mjs --check \"**/*.{js,jsx,ts,tsx,mdx}\"", "format:write": "prettier --config ./config/prettier.config.mjs --write \"**/*.{js,jsx,ts,tsx,mdx}\"", "lint-fix": "eslint --config ./config/eslint.config.mjs --fix", "lint": "eslint --config ./config/eslint.config.mjs . --max-warnings=10 && echo '✔ Linting complete. No issues found!'", "prepare": "husky", "sonar:local": "./scripts/run-sonar-local.sh", "start": "PORT=4000 next start", "storybook:clean": "rm -rf node_modules/.cache/storybook && rm -rf .next/cache && rm -rf .storybook/cache && rm -rf node_modules/.vite", "storybook:fresh": "npm run storybook:clean && npm run storybook", "storybook": "storybook dev -p 6006", "test:coverage": "vitest --config ./config/vitest.config.mts --coverage", "test:storybook": "vitest --config ./config/vitest.config.mts --run --project storybook", "test:unit": "vitest --config ./config/vitest.config.mts --typecheck --run --project unit", "test": "vitest --config ./config/vitest.config.mts --typecheck --run", "type-check": "npx tsc --noEmit", "validate": "npm-run-all --parallel lint type-check test"}, "dependencies": {"@base-ui-components/react": "^1.0.0-alpha.6", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@fontsource/inter": "5.0.16", "@heroicons/react": "2.1.1", "@hookform/resolvers": "3.3.2", "@mui/icons-material": "^7.1.2", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.1.2", "@mui/material-nextjs": "^7.1.1", "@mui/x-charts-pro": "^8.6.0", "@mui/x-data-grid-premium": "^8.6.0", "@mui/x-date-pickers-pro": "^8.6.0", "@mui/x-tree-view-pro": "^8.6.0", "@reduxjs/toolkit": "2.8.2", "change-case": "^5.4.4", "date-fns": "^4.1.0", "i18next": "^23.7.11", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "jss": "^10.10.0", "jss-rtl": "^0.3.0", "lodash.isequal": "4.5.0", "next": "^15.2.2", "npm": "^11.2.0", "nprogress": "^0.2.0", "numeral": "^2.0.6", "re-resizable": "^6.11.2", "react": "19.1.0", "react-circular-progressbar": "^2.2.0", "react-country-flag": "^3.1.0", "react-countup": "^6.5.0", "react-dom": "19.1.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.0", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-input-mask": "^2.0.4", "react-redux": "^9.0.4", "react-syntax-highlighter": "^15.6.1", "redux": "^5.0.0", "redux-thunk": "^3.1.0", "simplebar-react": "^3.2.4", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "swiper": "^11.0.5", "tss-react": "^4.9.15", "zod": "^3.24.2"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@ianvs/prettier-plugin-sort-imports": "4.1.1", "@storybook/addon-docs": "^9.0.12", "@storybook/addon-onboarding": "^9.0.12", "@storybook/addon-themes": "^9.0.12", "@storybook/addon-vitest": "9.0.12", "@storybook/nextjs-vite": "^9.0.12", "@storybook/react": "^9.0.12", "@svgr/webpack": "8.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@total-typescript/ts-reset": "^0.6.1", "@types/d3-color": "^3.1.3", "@types/hast": "^3.0.4", "@types/js-cookie": "3.0.3", "@types/lodash.isequal": "4.5.8", "@types/node": "22.13.10", "@types/nprogress": "0.2.3", "@types/numeral": "2.0.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@types/react-input-mask": "3.0.5", "@types/react-redux": "7.1.33", "@types/react-syntax-highlighter": "15.5.13", "@types/unist": "^3.0.3", "@vitejs/plugin-react": "^4.5.2", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.22.0", "eslint-config-next": "^15.2.2", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-storybook": "^9.0.12", "globals": "^16.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "npm-run-all": "^4.1.5", "playwright": "^1.53.0", "prettier": "^3.5.3", "storybook": "^9.0.12", "typescript": "^5.8.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["prettier --config ./config/prettier.config.mjs --write", "eslint --config ./config/eslint.config.mjs --fix"], "**/*.{json,css,md,mdx}": ["prettier --config ./config/prettier.config.mjs --write"], "*.{ts,tsx}": "vitest related --run --config ./config/vitest.config.mts"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}