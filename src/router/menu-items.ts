import type { ChangeCaseTransform } from '@/components/base/typography';
import { type ReactNode } from 'react';
import ROUTES from './routes';

/**
 * Menu item structure for the sidebar navigation
 */
export interface MenuItem {
  /**
   * The title of the menu item
   */
  title: string;

  /**
   * The path to navigate to when the menu item is clicked
   */
  path?: string;

  /**
   * The icon to display next to the menu item
   */
  icon?: ReactNode;

  /**
   * Whether the menu item is a header (section title)
   */
  isHeader?: boolean;

  /**
   * Submenu items
   */
  items?: MenuItem[];

  /**
   * Text transformation for the menu item title
   */
  caseTransform?: ChangeCaseTransform;
}

/**
 * Generate menu items with translations
 *
 * This can be extended to include role-based filtering in the future.
 *
 * @param t Translation function
 * @returns Array of menu items
 */
export const getMenuItems = (t: (token: string) => string): MenuItem[] => {
  return [
    {
      title: t('common.label.general'),
      isHeader: true,
      items: [
        {
          title: t('common.label.dashboard'),
          path: ROUTES.DASHBOARD,
        },
      ],
    },
    {
      title: t('common.label.userManagement'),
      isHeader: true,
      items: [
        {
          title: t('common.label.user'),
          path: ROUTES.USER_MANAGEMENT.USER,
        },
        {
          title: t('common.label.rnp'),
          path: '',
        },
        {
          title: t('common.label.department'),
          path: '',
        },
      ],
    },
    {
      title: t('common.label.memManagement'),
      isHeader: true,
      items: [
        {
          title: t('common.label.memAccManagement'),
          items: [
            {
              title: t('common.label.memberAcc'),
              path: '',
            },
            {
              title: t('common.label.memVerification'),
              path: '',
            },
          ],
        },
        {
          title: t('common.label.memSegmentation'),
          items: [
            {
              title: t('common.label.riskGroup'),
              path: '',
            },
            {
              title: t('common.label.vipTier'),
              caseTransform: 'none',
              path: '',
            },
          ],
        },
      ],
    },
    {
      title: t('common.label.logNMonitoring'),
      isHeader: true,
      items: [
        {
          title: t('common.label.auditTrail'),
          path: ROUTES.LOG_MONITORING.AUDIT_TRAIL,
        },
      ],
    },
    {
      title: t('common.label.setting'),
      isHeader: true,
      items: [
        {
          title: t('common.label.generalSetting'),
          items: [
            {
              title: t('common.label.currency'),
              path: '',
            },
            {
              title: t('common.label.theme'),
              path: '',
            },
          ],
        },
        {
          title: t('common.label.securityControl'),
          items: [
            {
              title: t('common.label.safety'),
              path: '',
            },
            {
              title: t('common.label.accessControl'),
              path: '',
            },
          ],
        },
      ],
    },
  ];
};

export default getMenuItems;
