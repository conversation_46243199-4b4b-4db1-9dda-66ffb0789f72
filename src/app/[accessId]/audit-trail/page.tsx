'use client';

import { FilterableSidebarLayout } from '@/components/application-ui/layouts/filterable-sidebar-layout';
import { AuditTrailFilterPanel } from '@/components/features/audit-trail/audit-trail-listing';
import { useAuditTrailFilters } from '@/components/features/audit-trail/hooks';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import transformText from '@/utils/text-transform.util';
import { type JSX } from 'react';
import { useTranslation } from 'react-i18next';

const pageMeta = {
  title: 'common.label.auditTrail',
  description: 'auditTrail.sentence.pageDesc',
};

/**
 * AuditPage component renders the audit trail page with a filterable sidebar layout.
 * It includes a filter panel for applying user filters and a data grid for displaying user data.
 *
 * @returns {JSX.Element} The rendered audit trail page component.
 */
const AuditPage = (): JSX.Element => {
  usePageTitle(pageMeta.title);
  const { t } = useTranslation();
  const translatedTitle = t(pageMeta.title);
  const transformedTitle = transformText(translatedTitle, 'sentenceCase');
  const translatedDescription = t(pageMeta.description);
  const transformedDescription = transformText(translatedDescription, 'sentenceCase');

  const { filterState, handleApplyFilters, handleResetFilters, handleFilterChange } =
    useAuditTrailFilters();

  const pageHeadingConfig = {
    title: transformedTitle,
    description: transformedDescription,
  };

  return (
    <FilterableSidebarLayout
      pageHeading={pageHeadingConfig}
      sidebarContent={
        <AuditTrailFilterPanel
          filters={{
            filters: filterState.filters,
          }}
          onFilterChange={handleFilterChange}
          onSearch={handleApplyFilters}
          onClear={handleResetFilters}
        />
      }
    >
      <></>
    </FilterableSidebarLayout>
  );
};

export default AuditPage;
