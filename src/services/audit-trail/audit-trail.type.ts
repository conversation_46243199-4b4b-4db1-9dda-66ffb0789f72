import { type ApiResponse } from '@/types/api-responses.type';

export type AuditTrailStatus = 'active' | 'inactive';

export interface AuditTrail {
  id: string;
  [key: string]: any;
}

/**
 * Filter parameters for audit trail
 */
export interface AuditTrailFilterParams {
  filters: {
    queryFilter: string[];
    datetime: string[];
  };
}

/**
 * Audit Trail service interface
 */
export interface AuditTrailService {
  /**
   * Get filtered audit trail based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered audit trail and error state
   */
  getFilteredAuditTrail(filters: AuditTrailFilterParams): Promise<ApiResponse<AuditTrail[]>>;
}
