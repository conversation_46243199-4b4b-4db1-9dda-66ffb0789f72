import { usersApi } from '@/mocks/users.mock';
import { type ApiResponse } from '@/types/api-responses.type';
import { wait } from '@/utils/wait.util';
import type { AuditTrail, AuditTrailFilterParams, AuditTrailService } from './audit-trail.type';

/**
 * User service for handling user-related operations
 */
class AuditTrailServiceImpl implements AuditTrailService {
  /**
   * Get filtered users based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered users and error state
   */
  getFilteredAuditTrail = async (
    filters: AuditTrailFilterParams
  ): Promise<ApiResponse<AuditTrail[]>> => {
    try {
      // Get users from mock API
      const response = await usersApi.getUsers();

      // Apply filters to the response
      let filteredUsers = response;

      // Apply status filter
      if (filters.filters.status.length > 0) {
        filteredUsers = filteredUsers.filter(
          (user) => user.status !== undefined && filters.filters.status.includes(user.status)
        );
      }

      // Simulate network delay
      await wait(500);

      return {
        data: [],
        message: 'data retrieved successfully',
        meta: {
          totalCount: filteredUsers.length,
        },
      };
    } catch (err) {
      return {
        message: err instanceof Error ? err.message : 'Unknown error',
        errorCode: 'ERR_FETCH_USERS',
      };
    }
  };
}

export const auditTrailService = new AuditTrailServiceImpl();
