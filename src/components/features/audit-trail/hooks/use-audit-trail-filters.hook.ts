import { useFilter } from '@/hooks/data/use-filter.hook';
import { type AuditTrailFilterParams } from '@/services/audit-trail';
import { useCallback, useState } from 'react';

/**
 * Hook for managing user filters
 * @returns Filter state and handlers
 */
export const useAuditTrailFilters = () => {
  // Filter state management using the useFilter hook
  const { filterState, updateFilter, resetFilters } = useFilter({
    queryFilter: [],
    datetime: [],
  });

  // Applied filters (what's actually used for filtering)
  const [appliedFilters, setAppliedFilters] = useState<AuditTrailFilterParams>({
    filters: {
      queryFilter: [],
      datetime: [],
    },
  });

  // Apply the current filter state to the applied filters
  const handleApplyFilters = useCallback(() => {
    setAppliedFilters({
      filters: {
        queryFilter: [...filterState.filters.queryFilter],
        datetime: [...filterState.filters.datetime],
      },
    });
  }, [filterState]);

  // Reset all filters
  const handleResetFilters = useCallback(() => {
    // Reset the filter state
    resetFilters();

    // Also reset the applied filters
    setAppliedFilters({
      filters: {
        queryFilter: [],
        datetime: [],
      },
    });
  }, [resetFilters]);

  // Handle filter changes from components
  const handleFilterChange = useCallback(
    (key: string, value: any) => {
      if (key === 'filters') {
        // When the entire filters object is updated from the filter component
        updateFilter('queryFilter', value.queryFilter);
        updateFilter('datetime', value.datetime);
      }
    },
    [updateFilter]
  );

  return {
    filterState,
    appliedFilters,
    handleApplyFilters,
    handleResetFilters,
    handleFilterChange,
  };
};

export default useAuditTrailFilters;
