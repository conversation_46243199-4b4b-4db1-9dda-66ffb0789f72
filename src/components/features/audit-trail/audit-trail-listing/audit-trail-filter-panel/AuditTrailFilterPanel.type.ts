import { type FilterState } from '@/hooks/data/use-filter.hook';

/**
 * Props for the AuditTrailFilterPanel component
 */
export interface AuditTrailFilterPanelProps {
  /**
   * The current filter state
   */
  filters: FilterState<{}>;

  /**
   * Call<PERSON> fired when a filter is changed
   */
  onFilterChange: (key: string, value: any) => void;

  /**
   * <PERSON><PERSON> fired when the search button is clicked
   */
  onSearch: () => void;

  /**
   * Callback fired when the clear button is clicked
   */
  onClear: () => void;
}

// FilterSectionProps has been moved to application-ui/filter-section/CheckboxFilterSection.types.ts
