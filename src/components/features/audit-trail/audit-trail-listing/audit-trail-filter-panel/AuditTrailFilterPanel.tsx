import {
  FilterActionButtons,
  FilterDateTimeRangePicker,
  FilterSmartSearch,
} from '@/components/application-ui/filter-section';
import type { Filter } from '@/components/application-ui/filter-section/filter-smart-search/FilterSmartSearch.type';
import { Box } from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { type AuditTrailFilterPanelProps } from './AuditTrailFilterPanel.type';

export const AuditTrailFilterPanel = ({
  filters,
  onFilterChange,
  onSearch,
  onClear,
}: AuditTrailFilterPanelProps) => {
  const { t } = useTranslation();

  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>(() => [null, null]);
  const [textFilters, setTextFilters] = useState<Filter[]>([]);

  const filterOptions = [
    { key: 'requestId', label: 'requestId:' },
    { key: 'module', label: 'module:' },
    { key: 'event', label: 'event:' },
    { key: 'actor', label: 'actor:' },
    { key: 'targetRefId', label: 'targetRefId:' },
    { key: 'ipAddress', label: 'ipAddress:' },
    { key: 'hostAddress', label: 'hostAddress:' },
    { key: 'status', label: 'status:' },
  ];

  const handleResetFilters = () => {
    setDateRange([null, null]);
    setTextFilters([]);
    onClear();
  };

  const handleDateChange = (newRange: [Date | null, Date | null]) => {
    setDateRange(newRange);
    onFilterChange('filters', {
      ...filters.filters,
      dateRange: newRange,
    });
  };

  const handleTextFiltersChange = (newFilters: Filter[]) => {
    setTextFilters(newFilters);
    onFilterChange('filters', {
      ...filters.filters,
      textFilters: newFilters,
    });
  };

  return (
    <Box>
      <FilterActionButtons
        onSearch={onSearch}
        onClear={handleResetFilters}
      />

      <FilterSmartSearch
        label={t('common.label.search')}
        filters={textFilters}
        filterOptions={filterOptions}
        onFiltersChange={handleTextFiltersChange}
        placeholder="e.g. status:active"
      />

      <FilterDateTimeRangePicker
        label={t('common.label.dateTime')}
        value={dateRange}
        onChange={handleDateChange}
      />
    </Box>
  );
};

export default AuditTrailFilterPanel;
