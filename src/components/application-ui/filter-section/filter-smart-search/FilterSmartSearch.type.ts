/**
 * Props for the FilterSmartSearch component.
 */
export interface FilterSmartSearchProps {
  /**
   * The label for the smart search filter.
   */
  label: string;
  /**
   * The available filter options that can be selected.
   */
  filterOptions: FilterOption[];

  /**
   * Sets a placeholder text for the input field in the smart search filter.
   *
   * @param placeholder - The placeholder text to be displayed when the input field is empty.
   * @returns The updated placeholder text.
   */
  placeholder?: string;
  /**
   * The currently applied filters.
   */
  filters: Filter[];

  /**
   * Callback function that is called when the filters change.
   * @param filters - The updated list of filters.
   */
  onFiltersChange: (filters: Filter[]) => void;
}

/**
 * Represents a filter option with a key and a label.
 */
export interface FilterOption {
  key: string;
  label: string;
}

/**
 * Represents a filter with a key and a value.
 */
export interface Filter {
  key: string;
  value: string;
}
