'use client';

import { DateTimeRangePicker } from '@/components/base/inputs/date-time-range-picker/DateTimeRangePicker';
import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { AccordionDetails, AccordionSummary, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import type { FilterDateTimeRangePickerProps } from './FilterDateTimeRangePicker.type';

/**
 * A component that renders a date-time range picker within an accordion.
 *
 * @param label - The label for the accordion, which will be translated.
 * @param value - The current value of the date-time range picker.
 * @param onChange - Callback function to handle changes in the date-time range picker.
 * @param defaultExpanded - Optional boolean to set the accordion's default expanded state.
 * @returns A JSX element containing the date-time range picker within an accordion.
 */
export const FilterDateTimeRangePicker = ({
  label,
  value,
  onChange,
  defaultExpanded = true,
}: FilterDateTimeRangePickerProps & { defaultExpanded?: boolean }) => {
  const { t } = useTranslation();
  return (
    <Accordion
      defaultExpanded={defaultExpanded}
      customStyle="minimal"
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography
          variant="h5"
          caseTransform="sentenceCase"
        >
          {t(label)}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box
          px={{ xs: 2 }}
          pb={2}
        >
          <DateTimeRangePicker
            value={value}
            onChange={onChange}
          />
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default FilterDateTimeRangePicker;
