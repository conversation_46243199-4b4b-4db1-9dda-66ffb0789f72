/**
 * Props for the FilterDateTimeRangePicker component.
 */
export interface FilterDateTimeRangePickerProps {
  /**
   * The label for the date-time range picker.
   */
  label: string;

  /**
   * The current value of the date-time range.
   * It is a tuple containing two Date objects or null.
   */
  value: [Date | null, Date | null];

  /**
   * Callback function that is called when the date-time range changes.
   *
   * @param range - The new date-time range, represented as a tuple of two Date objects or null.
   */
  onChange: (range: [Date | null, Date | null]) => void;
}
