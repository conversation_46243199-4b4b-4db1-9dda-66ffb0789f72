import type { Meta, StoryObj } from '@storybook/react';
import { addHours } from 'date-fns';
import React, { useState } from 'react';
import { FilterDateTimeRangePicker } from './FilterDateTimeRangePicker';
import type { FilterDateTimeRangePickerProps } from './FilterDateTimeRangePicker.type';

const meta: Meta<typeof FilterDateTimeRangePicker> = {
  title: 'Components/application-ui/filter-section/FilterDateTimeRange',
  component: FilterDateTimeRangePicker,
  tags: ['autodocs'],
  args: {
    label: 'common.label.dateTime',
    defaultExpanded: true,
  },
};

export default meta;

type Story = StoryObj<typeof FilterDateTimeRangePicker>;

/**
 * Wrapper component for the FilterDateTimeRangePicker.
 *
 * @param args - The props for the FilterDateTimeRangePicker component, including an optional defaultExpanded flag.
 * @returns A JSX element rendering the FilterDateTimeRangePicker with controlled value state.
 */
const Wrapper = (args: FilterDateTimeRangePickerProps & { defaultExpanded?: boolean }) => {
  const [value, setValue] = useState<[Date | null, Date | null]>([
    new Date(),
    addHours(new Date(), 1),
  ]);

  return (
    <FilterDateTimeRangePicker
      {...args}
      value={value}
      onChange={setValue}
    />
  );
};

export const Default: Story = {
  render: (args) => <Wrapper {...args} />,
};
