import { styled } from '@mui/material/styles';
import { MultiInputDateTimeRangeField } from '@mui/x-date-pickers-pro/MultiInputDateTimeRangeField';

/**
 * A styled component that customizes the appearance of the MultiInputDateTimeRangeField.
 * Defaults to column direction, but allows overriding with props.
 */
export const StyledMultiInputDateTimeRangeField = styled(MultiInputDateTimeRangeField)(
  ({ theme }) => ({
    display: 'flex',
    position: 'relative',
    alignItems: 'center',
    flexDirection: 'column',

    [`& .MuiMultiInputDateTimeRangeField-separator`]: {
      visibility: 'hidden',
      width: theme.spacing(1),
    },
    '& > :not(style) ~ :not(style)': {
      marginLeft: 0,
    },
  })
);
