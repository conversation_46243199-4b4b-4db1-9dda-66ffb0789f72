import { LocalizationProvider } from '@mui/x-date-pickers';
import { DateTimeRangePicker as MuiDateTimeRangePicker } from '@mui/x-date-pickers-pro/DateTimeRangePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { type DateTimeRangePickerProps } from './DateTimeRangePicker.type';
import { StyledMultiInputDateTimeRangeField } from './styles';

/**
 * A base component for selecting a range of date and time values.
 *
 * @param {Object} props - The component props.
 * @param {[Date | null, Date | null]} props.value - The current value of the date-time range picker.
 * @param {function} props.onChange - Callback function to handle changes to the selected date-time range.
 * @param {boolean} [props.disableFuture=false] - If true, disables selection of future dates.
 * @param {boolean} [props.disablePast=false] - If true, disables selection of past dates.
 * @param {string} [props.format='dd/MM/yyyy HH:mm:ss'] - The format string for displaying the date and time.
 * @param {Object} [props.slots={ field: MultiInputDateTimeRangeField }] - Custom slots for the date-time range picker.
 * @returns {JSX.Element} A JSX element representing the date-time range picker.
 */
export const DateTimeRangePicker = ({
  value,
  onChange,
  localeText = { start: 'Start', end: 'End' },
  disableFuture = false,
  disablePast = false,
  views = ['day', 'hours', 'minutes', 'seconds'],
  closeOnSelect = true,
  format = 'dd/MM/yyyy HH:mm:ss',
  ampm = false,
  slots = { field: StyledMultiInputDateTimeRangeField },
}: DateTimeRangePickerProps): React.JSX.Element => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <MuiDateTimeRangePicker
        value={value}
        onChange={onChange}
        slots={slots}
        localeText={localeText}
        disableFuture={disableFuture}
        disablePast={disablePast}
        format={format}
        ampm={ampm}
        closeOnSelect={closeOnSelect}
        views={views}
      />
    </LocalizationProvider>
  );
};

export default DateTimeRangePicker;
