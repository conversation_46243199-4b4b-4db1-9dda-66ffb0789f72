import { DateTimeRangePicker as MuiDateTimeRangePicker } from '@mui/x-date-pickers-pro/DateTimeRangePicker';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { type DateTimeRangePickerProps } from './DateTimeRangePicker.type';
import { StyledMultiInputDateTimeRangeField } from './styles';

/**
 * A base component for selecting a range of date and time values.
 *
 * Features:
 * - Internationalization support with automatic translation
 * - Proper error handling and validation
 * - Accessibility support
 * - Consistent styling with the design system
 *
 * @param props - The component props
 * @returns A JSX element representing the date-time range picker
 *
 * @example
 * ```tsx
 * const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
 *
 * <DateTimeRangePicker
 *   value={dateRange}
 *   onChange={setDateRange}
 *   disableFuture
 *   error={!!validationError}
 *   helperText={validationError}
 * />
 * ```
 */
export const DateTimeRangePicker = ({
  value,
  onChange,
  localeText,
  disableFuture = false,
  disablePast = false,
  views = ['day', 'hours', 'minutes', 'seconds'],
  closeOnSelect = true,
  format = 'dd/MM/yyyy HH:mm:ss',
  ampm = false,
  slots = { field: StyledMultiInputDateTimeRangeField },
  error = false,
  helperText,
  disabled = false,
  readOnly = false,
  onError,
  ...otherProps
}: DateTimeRangePickerProps): React.JSX.Element => {
  const { t } = useTranslation();

  // Memoize translated locale text to avoid unnecessary re-renders
  const translatedLocaleText = useMemo(() => {
    if (localeText) {
      return localeText;
    }

    return {
      start: t('common.label.startDate'),
      end: t('common.label.endDate'),
    };
  }, [localeText, t]);

  // Handle validation and error reporting
  const handleChange = (newValue: [Date | null, Date | null]) => {
    // Basic validation: ensure start date is before end date
    if (newValue[0] && newValue[1] && newValue[0] > newValue[1]) {
      onError?.('Start date must be before end date');
      return;
    }

    // Clear any previous errors
    onError?.(null);
    onChange(newValue);
  };

  return (
    <MuiDateTimeRangePicker
      value={value}
      onChange={handleChange}
      slots={slots}
      localeText={translatedLocaleText}
      disableFuture={disableFuture}
      disablePast={disablePast}
      format={format}
      ampm={ampm}
      closeOnSelect={closeOnSelect}
      views={views}
      disabled={disabled}
      readOnly={readOnly}
      slotProps={{
        textField: {
          error,
          helperText,
        },
        ...otherProps.slotProps,
      }}
      {...otherProps}
    />
  );
};

export default DateTimeRangePicker;
