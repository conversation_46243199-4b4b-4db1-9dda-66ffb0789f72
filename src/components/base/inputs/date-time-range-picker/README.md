# DateTimeRangePicker Component

A comprehensive date-time range picker component with internationalization support, validation, and accessibility features.

## Features

- ✅ **Internationalization**: Automatic translation support via react-i18next
- ✅ **Validation**: Built-in validation (start date before end date)
- ✅ **Error Handling**: Error display and callback support
- ✅ **Accessibility**: Full accessibility support
- ✅ **Consistent Styling**: Follows design system patterns
- ✅ **Type Safety**: Full TypeScript support with proper type definitions

## Usage

### Basic Usage

```tsx
import { DateTimeRangePicker } from '@/components/base/inputs/date-time-range-picker';

const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

<DateTimeRangePicker
  value={dateRange}
  onChange={setDateRange}
/>
```

### With Validation and Error Handling

```tsx
const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
const [error, setError] = useState<string | null>(null);

<DateTimeRangePicker
  value={dateRange}
  onChange={setDateRange}
  onError={setError}
  error={!!error}
  helperText={error}
/>
```

### With Custom Configuration

```tsx
<DateTimeRangePicker
  value={dateRange}
  onChange={setDateRange}
  disableFuture
  format="MM/dd/yyyy hh:mm a"
  ampm
  localeText={{ start: 'From', end: 'To' }}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `[Date \| null, Date \| null]` | - | The current date range value |
| `onChange` | `(range: [Date \| null, Date \| null]) => void` | - | Callback fired when the date range changes |
| `localeText` | `{ start: string; end: string }` | Auto-translated | Custom labels for start and end inputs |
| `format` | `string` | `'dd/MM/yyyy HH:mm:ss'` | The format string for displaying dates |
| `ampm` | `boolean` | `false` | If true, uses 12-hour format with AM/PM |
| `disableFuture` | `boolean` | `false` | If true, disables selection of future dates |
| `disablePast` | `boolean` | `false` | If true, disables selection of past dates |
| `disabled` | `boolean` | `false` | If true, the component is disabled |
| `readOnly` | `boolean` | `false` | If true, the component is read-only |
| `error` | `boolean` | `false` | If true, the input displays an error state |
| `helperText` | `string` | - | Helper text to display below the input |
| `onError` | `(error: string \| null) => void` | - | Callback fired when a validation error occurs |
| `closeOnSelect` | `boolean` | `true` | If true, closes the picker after selecting a date |
| `views` | `Array<'day' \| 'hours' \| 'minutes' \| 'seconds'>` | `['day', 'hours', 'minutes', 'seconds']` | Views available in the picker |

## Improvements Made

### 1. Internationalization Integration
- Removed hardcoded English labels
- Added automatic translation support using `useTranslation` hook
- Falls back to custom `localeText` if provided

### 2. Removed LocalizationProvider Redundancy
- Removed nested `LocalizationProvider` wrapper
- Relies on app-level provider in `DocumentLayout`
- Prevents potential conflicts and improves performance

### 3. Enhanced Error Handling and Validation
- Added built-in validation for date range logic
- Added `error` and `helperText` props for error display
- Added `onError` callback for custom error handling
- Validates that start date is before end date

### 4. Improved Type Safety
- Enhanced type definitions with better documentation
- Added missing props like `disabled`, `readOnly`, `error`, etc.
- Fixed type conflicts with MUI's base component
- Added proper JSDoc comments with examples

### 5. Better Accessibility and UX
- Added proper error states and helper text
- Added disabled and read-only states
- Improved component documentation and examples

### 6. Enhanced Storybook Stories
- Added comprehensive story variants
- Added proper decorators for LocalizationProvider
- Added interactive examples with state management
- Added documentation and descriptions

### 7. Testing Support
- Added basic test file with testing patterns
- Mocked i18n dependencies properly
- Provided examples for testing different states

## Migration Guide

If you're upgrading from the previous version:

1. **Remove LocalizationProvider**: The component no longer needs to be wrapped in `LocalizationProvider`
2. **Update imports**: No changes needed for imports
3. **Add error handling**: Consider adding `onError`, `error`, and `helperText` props for better UX
4. **Update tests**: Use the new test patterns with proper mocking

## Best Practices

1. **Always handle errors**: Use the `onError` callback to provide user feedback
2. **Use translations**: Let the component handle translations automatically
3. **Provide helper text**: Use `helperText` for additional context
4. **Test thoroughly**: Use the provided test patterns as a starting point
5. **Follow accessibility guidelines**: The component includes proper ARIA attributes
