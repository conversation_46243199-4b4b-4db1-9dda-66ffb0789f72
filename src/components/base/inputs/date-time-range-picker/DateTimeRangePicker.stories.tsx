import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import React from 'react';
import { DateTimeRangePicker } from './DateTimeRangePicker';

const meta: Meta<typeof DateTimeRangePicker> = {
  title: 'Components/base/inputs/DateTimeRangePicker',
  component: DateTimeRangePicker,
  argTypes: {
    value: {
      control: 'object',
      defaultValue: [null, null],
    },
    onChange: { action: 'changed' },
    localeText: {
      control: 'text',
      defaultValue: { start: 'Start', end: 'End' },
    },
    disableFuture: {
      control: 'boolean',
      defaultValue: false,
    },
    disablePast: {
      control: 'boolean',
      defaultValue: false,
    },
    format: {
      control: 'text',
      defaultValue: 'dd/MM/yyyy HH:mm:ss',
    },
    slots: {
      control: 'object',
      defaultValue: { field: 'MultiInputDateTimeRangeField' },
    },
  },
};

export default meta;

type Story = StoryObj<typeof DateTimeRangePicker>;

export const Default: Story = {
  /**
   * Renders the DateTimeRangePicker component with the provided arguments.
   *
   * @param args - The arguments to configure the DateTimeRangePicker component.
   * @returns A JSX element rendering the DateTimeRangePicker.
   */
  render: (args) => <DateTimeRangePicker {...args} />,
};
