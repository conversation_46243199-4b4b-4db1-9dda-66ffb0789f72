import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import React, { useState } from 'react';
import { DateTimeRangePicker } from './DateTimeRangePicker';

const meta: Meta<typeof DateTimeRangePicker> = {
  title: 'Components/base/inputs/DateTimeRangePicker',
  component: DateTimeRangePicker,
  decorators: [
    (Story) => (
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Story />
      </LocalizationProvider>
    ),
  ],
  argTypes: {
    value: {
      control: 'object',
      description: 'The current date range value as [startDate, endDate]',
    },
    onChange: {
      action: 'changed',
      description: 'Callback fired when the date range changes',
    },
    localeText: {
      control: 'object',
      description:
        'Custom labels for start and end inputs. If not provided, uses i18n translations.',
    },
    disableFuture: {
      control: 'boolean',
      description: 'If true, disables selection of future dates',
    },
    disablePast: {
      control: 'boolean',
      description: 'If true, disables selection of past dates',
    },
    disabled: {
      control: 'boolean',
      description: 'If true, the component is disabled',
    },
    readOnly: {
      control: 'boolean',
      description: 'If true, the component is read-only',
    },
    error: {
      control: 'boolean',
      description: 'If true, the input displays an error state',
    },
    helperText: {
      control: 'text',
      description: 'Helper text to display below the input',
    },
    format: {
      control: 'text',
      description: 'The format string for displaying dates',
    },
    ampm: {
      control: 'boolean',
      description: 'If true, uses 12-hour format with AM/PM',
    },
    closeOnSelect: {
      control: 'boolean',
      description: 'If true, closes the picker after selecting a date',
    },
    onError: {
      action: 'error',
      description: 'Callback fired when a validation error occurs',
    },
  },
  parameters: {
    docs: {
      description: {
        component: `
A date-time range picker component with internationalization support, validation, and accessibility features.

## Features
- Automatic translation support via react-i18next
- Built-in validation (start date before end date)
- Error handling and display
- Accessibility support
- Consistent styling with the design system
        `,
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof DateTimeRangePicker>;

export const Default: Story = {
  args: {
    value: [null, null],
    disableFuture: false,
    disablePast: false,
    disabled: false,
    readOnly: false,
    error: false,
    format: 'dd/MM/yyyy HH:mm:ss',
    ampm: false,
    closeOnSelect: true,
  },
  render: (args) => {
    const [value, setValue] = useState<[Date | null, Date | null]>(args.value || [null, null]);
    const [error, setError] = useState<string | null>(null);

    return (
      <DateTimeRangePicker
        {...args}
        value={value}
        onChange={setValue}
        onError={setError}
        error={!!error}
        helperText={error || args.helperText}
      />
    );
  },
};

export const WithError: Story = {
  args: {
    ...Default.args,
    error: true,
    helperText: 'Please select a valid date range',
  },
  render: Default.render,
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
    value: [new Date('2024-01-01'), new Date('2024-01-31')],
  },
  render: Default.render,
};

export const ReadOnly: Story = {
  args: {
    ...Default.args,
    readOnly: true,
    value: [new Date('2024-01-01'), new Date('2024-01-31')],
  },
  render: Default.render,
};

export const DisableFuture: Story = {
  args: {
    ...Default.args,
    disableFuture: true,
  },
  render: Default.render,
};

export const DisablePast: Story = {
  args: {
    ...Default.args,
    disablePast: true,
  },
  render: Default.render,
};

export const CustomFormat: Story = {
  args: {
    ...Default.args,
    format: 'MM/dd/yyyy hh:mm a',
    ampm: true,
  },
  render: Default.render,
};
