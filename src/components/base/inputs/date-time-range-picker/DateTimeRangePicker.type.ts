import type { DateTimeRangePickerProps as MuiDateTimeRangePickerProps } from '@mui/x-date-pickers-pro/DateTimeRangePicker';
import type { StyledMultiInputDateTimeRangeField } from './styles';

/**
 * Props for the DateTimeRangePicker component
 */
export interface DateTimeRangePickerProps
  extends Omit<MuiDateTimeRangePickerProps, 'value' | 'onChange' | 'localeText' | 'onError'> {
  /**
   * The current value of the date-time range picker, represented as a tuple of two dates.
   * Each date can be a Date object or null.
   */
  value: [Date | null, Date | null];

  /**
   * Callback function that is called when the date-time range changes.
   * @param range - A tuple containing the new start and end dates, each of which can be a Date object or null.
   */
  onChange: (range: [Date | null, Date | null]) => void;

  /**
   * The format string used to display the date-time values.
   * @default 'dd/MM/yyyy HH:mm:ss'
   */
  format?: string;

  /**
   * Determines whether the time should be displayed in 12-hour format with AM/PM.
   * @default false - 24-hour format
   */
  ampm?: boolean;

  /**
   * Represents localized text for the start and end labels in the date-time range picker.
   * If not provided, will use translated labels from i18n.
   *
   * @property start - The label for the start date input field.
   * @property end - The label for the end date input field.
   */
  localeText?: { start: string; end: string };

  /**
   * Determines whether the date-time picker should close automatically after a date is selected.
   * @default true
   */
  closeOnSelect?: boolean;

  /**
   * Specifies the views available in the date-time picker.
   * @default ['day', 'hours', 'minutes', 'seconds']
   */
  views?: Array<'day' | 'hours' | 'minutes' | 'seconds'>;

  /**
   * If true, disables the selection of future dates.
   * @default false
   */
  disableFuture?: boolean;

  /**
   * If true, disables the selection of past dates.
   * @default false
   */
  disablePast?: boolean;

  /**
   * If true, the component is disabled.
   * @default false
   */
  disabled?: boolean;

  /**
   * If true, the component is read-only.
   * @default false
   */
  readOnly?: boolean;

  /**
   * If true, the input will display an error state.
   * @default false
   */
  error?: boolean;

  /**
   * The helper text content.
   */
  helperText?: string;

  /**
   * Callback fired when an error occurs.
   * @param error - The error message or null if no error
   */
  onError?: (error: string | null) => void;

  /**
   * Optional slots for customizing the component's elements.
   * @property field - A React component type used to render the input field.
   */
  slots?: {
    field: typeof StyledMultiInputDateTimeRangeField;
  };
}
