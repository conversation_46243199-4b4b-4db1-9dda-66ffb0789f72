import transformText from '@/utils/text-transform.util';
import { enUS, zhCN } from '@mui/x-data-grid/locales';
import type { TFunction } from 'i18next';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import * as DataGridUtil from './DataGrid.util';

vi.mock('@/utils/text-transform.util');

const mockDate = new Date('2025-01-01T12:34:56.789Z');
const originalDate = global.Date;

describe('DataGrid', () => {
  describe('formatDateTime', () => {
    beforeEach(() => {
      vi.clearAllMocks();

      global.Date = vi.fn(() => mockDate) as unknown as typeof Date;
    });

    afterEach(() => {
      global.Date = originalDate;
    });

    it('should format the date to BO standard format', () => {
      const result = DataGridUtil.formatDateTime();

      expect(result).toBe('2025-01-01_12-34-56');
    });
  });

  describe('getFormattedFileName', () => {
    const datetime = '2025-01-01_12-34-56';

    beforeEach(() => {
      vi.clearAllMocks();

      global.Date = vi.fn(() => mockDate) as unknown as typeof Date;
    });

    afterEach(() => {
      global.Date = originalDate;
    });

    it('should return filename with filename_suffix_date format', () => {
      const baseFileName = 'report';
      const suffix = 'user';

      const result = DataGridUtil.getFormattedFileName(baseFileName, suffix);

      expect(result).toBe(`${baseFileName}_${suffix}_${datetime}`);
    });

    it('should return filename without suffix if not supplied', () => {
      const baseFileName = 'report';

      const result = DataGridUtil.getFormattedFileName(baseFileName);

      expect(result).toBe(`${baseFileName}_${datetime}`);
    });
  });

  describe('createHeaderName', () => {
    const fieldName = 'common.label.User';
    const mockResult = 'Transformed text';
    let mockT: (key: string) => string;

    beforeEach(() => {
      vi.clearAllMocks();

      mockT = vi.fn((key) => `Translated ${key}`);

      vi.mocked(transformText).mockReturnValue(mockResult);
    });

    it('should transform field name with translation and specified case', () => {
      const transform = 'uppercase';

      const result = DataGridUtil.createHeaderName(fieldName, mockT, transform);

      expect(mockT).toHaveBeenCalledWith(fieldName);
      expect(transformText).toHaveBeenCalledWith(mockT(fieldName), transform);
      expect(result).toEqual(mockResult);
    });

    it('should use default sentenceCase when transform is not supplied', () => {
      const transform = 'sentenceCase';

      const result = DataGridUtil.createHeaderName(fieldName, mockT);

      expect(mockT).toHaveBeenCalledWith(fieldName);
      expect(transformText).toHaveBeenCalledWith(mockT(fieldName), transform);
      expect(result).toEqual(mockResult);
    });
  });

  describe('useLocaleText', () => {
    const mockT: (key: string) => string = vi.fn();

    beforeEach(() => {
      vi.clearAllMocks();

      vi.mocked(mockT).mockImplementation((key: string) => {
        return `t_${key}`;
      });
    });

    it('should return English locale text for en', () => {
      const result = DataGridUtil.useLocaleText('en', mockT as TFunction);

      expect(result).toEqual(enUS.components.MuiDataGrid.defaultProps.localeText);
    });

    it('should return Chinese locale text with custom overrides', () => {
      const result = DataGridUtil.useLocaleText('zh', mockT as TFunction);

      expect(result).toMatchObject(zhCN.components.MuiDataGrid.defaultProps.localeText);
    });

    it('should return English locale text for any non-Chinese language', () => {
      const result = DataGridUtil.useLocaleText('bn', mockT as TFunction);

      expect(result).toEqual(enUS.components.MuiDataGrid.defaultProps.localeText);
    });
  });
});
