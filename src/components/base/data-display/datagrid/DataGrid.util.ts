import transformText from '@/utils/text-transform.util';
import { enUS, zhCN } from '@mui/x-data-grid/locales';
import type { TFunction } from 'i18next';
import type { ChangeCaseTransform } from '../../typography/Typography.type';

/**
 * Formats current datetime for file naming
 */
export const formatDateTime = () =>
  // TODO: Handle user timezone and locale
  new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').slice(0, -5);

/**
 * Formats a filename with the current datetime and optional suffix.
 */
export const getFormattedFileName = (baseFileName: string, suffix?: string) => {
  const datetime = formatDateTime();
  if (!suffix) return `${baseFileName}_${datetime}`;

  return `${baseFileName}_${suffix}_${datetime}`;
};

/**
 * Helper function to create a header name from a field name with translation and text transformation
 * @param fieldName - The field name to transform
 * @param t - Translation function
 * @returns Transformed header name
 */
export const createHeaderName = (
  fieldName: string,
  t: (key: string) => string,
  transform?: ChangeCaseTransform
): string => {
  return transformText(t(fieldName), transform ?? 'sentenceCase');
};

/**
 * Function to translate string for DataGrid
 *
 * (Ctrl / Command + P)
 * [EN]: node_modules/@mui/x-data-grid/esm/constants/localeTextConstants.js
 * [ZH]: node_modules/@mui/x-data-grid/esm/locales/zhCN.js
 */
export const useLocaleText = (language: Language, t: TFunction) => {
  if (language === 'zh') {
    const base = zhCN.components.MuiDataGrid.defaultProps.localeText;
    return {
      ...base,
      // if a string is not translated by MUI yet, we need to replace it manually
      // columnMenuManagePivot: t('common.action.managePivot'),
    };
  }

  // Default to 'en'
  return enUS.components.MuiDataGrid.defaultProps.localeText;
};
