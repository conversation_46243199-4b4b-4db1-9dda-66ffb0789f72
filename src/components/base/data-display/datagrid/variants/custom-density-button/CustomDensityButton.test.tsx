import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom';
import { DataGridPremium, Toolbar } from '@mui/x-data-grid-premium';
import { type FC } from 'react';
import CustomDensityButton from './CustomDensityButton';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'common.action.selectDensity': 'select density',
        'common.label.comfortable': 'comfortable',
        'common.label.compact': 'compact',
        'common.label.density': 'density',
        'common.label.standard': 'standard',
      };
      return translations[key] ?? key;
    },
  }),
}));

// Mock the grid API
const mockSetDensity = vi.fn();
const mockGridApiRef = {
  current: {
    setDensity: mockSetDensity,
  },
};

vi.mock('@mui/x-data-grid-premium', async () => {
  const actual = await vi.importActual('@mui/x-data-grid-premium');

  return {
    ...actual,
    // Override only the specific function want to mock
    gridDensitySelector: 'gridDensitySelector',
    useGridApiContext: () => mockGridApiRef,
    useGridSelector: () => 'standard', // Default to standard density
  };
});

describe('CustomDensityButton', () => {
  let TestComponent: FC;

  beforeEach(() => {
    vi.clearAllMocks();

    TestComponent = () => {
      // eslint-disable-next-line
      const CustomToolbarComponent = () => (
        <Toolbar>
          <CustomDensityButton />
        </Toolbar>
      );

      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={{
            toolbar: CustomToolbarComponent,
          }}
        />
      );
    };
  });

  it('calls setDensity when a menu item is clicked', async () => {
    const mockSelectOption = 'compact';

    // Render the component
    const component = render(<TestComponent />);

    // Click on the density button
    fireEvent.click(component.getByLabelText('Select density'));

    // Wait for the menu to appear
    await waitFor(() => screen.getByRole('menu'));

    // Click on the menu item
    fireEvent.click(component.getByText('Compact'));

    // Check that setDensity was called with the correct value
    expect(mockSetDensity).toHaveBeenCalledWith(mockSelectOption);

    // Menu should be closed after selection
    expect(component.queryByRole('menu')).not.toBeInTheDocument();
  });

  it('handles enter event with keyboard navigation in the menu', async () => {
    const user = userEvent.setup();

    // Render the component
    const component = render(<TestComponent />);

    // Click on the density button
    userEvent.click(component.getByLabelText('Select density'));

    // Wait for the menu to appear
    await waitFor(() => screen.getByRole('menu'));

    // Press down arrow to navigate to the next item
    await user.keyboard('{ArrowDown}');

    // Press Enter to select the item
    await user.keyboard('{Enter}');

    // Check that setDensity was called with the correct value (comfortable)
    expect(mockSetDensity).toHaveBeenCalledWith('comfortable');

    // Menu should be closed after selection
    expect(component.queryByRole('menu')).not.toBeInTheDocument();
  });
});
