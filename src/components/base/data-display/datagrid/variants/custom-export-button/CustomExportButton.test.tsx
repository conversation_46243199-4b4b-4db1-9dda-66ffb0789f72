import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom';
import {
  DataGridPremium,
  gridExpandedSortedRowIdsSelector,
  gridPaginatedVisibleSortedGridRowIdsSelector,
  gridSortedRowIdsSelector,
  Toolbar,
} from '@mui/x-data-grid-premium';
import { useState, type FC } from 'react';
import CustomExportButton, { exportForUnitTest } from './CustomExportButton';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'common.action.exportAll': 'export all',
        'common.action.exportCurrentPage': 'export current page',
        'common.action.exportFiltered': 'export filtered',
        'common.action.selectExport': 'select export',
        'common.label.export': 'export',
      };
      return translations[key] ?? key;
    },
  }),
}));

// Mock the grid API
const mockExportDataAsCsv = vi.fn((options) => {
  if (options.getRowsToExport) {
    const mockParams = { apiRef: mockGridApiRef.current };
    options.getRowsToExport(mockParams);
  }
});
const mockGridApiRef = {
  current: {
    exportDataAsCsv: mockExportDataAsCsv,
  },
};

vi.mock('@mui/x-data-grid-premium', async () => {
  const actual = await vi.importActual('@mui/x-data-grid-premium');

  return {
    ...actual,
    // Override only the specific function want to mock
    useGridApiContext: () => mockGridApiRef,
    gridPaginatedVisibleSortedGridRowIdsSelector: vi.fn(),
    gridSortedRowIdsSelector: vi.fn(),
    gridExpandedSortedRowIdsSelector: vi.fn(),
  };
});

describe('CustomExportButton', () => {
  let csvOptions: { fileName?: string } = {};
  let TestComponent: FC;

  beforeEach(() => {
    vi.clearAllMocks();

    csvOptions = {
      fileName: 'test-export',
    };

    TestComponent = () => {
      const [options] = useState(csvOptions);

      // eslint-disable-next-line
      const CustomToolbarComponent = () => (
        <Toolbar>
          <CustomExportButton csvOptions={options} />
        </Toolbar>
      );

      return (
        <DataGridPremium
          columns={[{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={{
            toolbar: CustomToolbarComponent,
          }}
        />
      );
    };
  });

  it('exports current page data when that option is selected', async () => {
    // Render the component
    const component = render(<TestComponent />);

    // Click on the export button
    fireEvent.click(component.getByLabelText('Select export'));

    // Wait for the menu to appear
    await waitFor(() => screen.getByRole('menu'));

    // Click export current page option
    fireEvent.click(component.getByText('Export current page'));

    // Check that exportDataAsCsv was called with the correct options
    expect(mockExportDataAsCsv).toHaveBeenCalledTimes(1);
    expect(mockExportDataAsCsv).toHaveBeenCalledWith({
      ...csvOptions,
      utf8WithBom: true,
      fileName: expect.stringMatching(
        `^${csvOptions.fileName}_current_page_\\d{4}-\\d{2}-\\d{2}_\\d{2}-\\d{2}-\\d{2}$`
      ),
      getRowsToExport: exportForUnitTest.exportTypes.current_page,
    });

    // Check correct export function is called
    expect(gridPaginatedVisibleSortedGridRowIdsSelector).toHaveBeenCalledTimes(1);

    // Menu should be closed after selection
    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
  });

  it('exports all data when that option is selected', async () => {
    // Render the component
    const component = render(<TestComponent />);

    // Click on the export button
    fireEvent.click(component.getByLabelText('Select export'));

    // Wait for the menu to appear
    await waitFor(() => screen.getByRole('menu'));

    // Click export all option
    fireEvent.click(component.getByText('Export all'));

    // Check that exportDataAsCsv was called with the correct options
    expect(mockExportDataAsCsv).toHaveBeenCalledTimes(1);
    expect(mockExportDataAsCsv).toHaveBeenCalledWith({
      ...csvOptions,
      utf8WithBom: true,
      fileName: expect.stringMatching(
        `^${csvOptions.fileName}_all_\\d{4}-\\d{2}-\\d{2}_\\d{2}-\\d{2}-\\d{2}$`
      ),
      getRowsToExport: exportForUnitTest.exportTypes.all,
    });

    // Check correct export function is called
    expect(gridSortedRowIdsSelector).toHaveBeenCalledTimes(1);

    // Menu should be closed after selection
    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
  });

  it('exports filtered data when that option is selected', async () => {
    // Render the component
    const component = render(<TestComponent />);

    // Click on the export button
    fireEvent.click(component.getByLabelText('Select export'));

    // Wait for the menu to appear
    await waitFor(() => screen.getByRole('menu'));

    // Click export filtered option
    fireEvent.click(component.getByText('Export filtered'));

    // Check that exportDataAsCsv was called with the correct options
    expect(mockExportDataAsCsv).toHaveBeenCalledTimes(1);
    expect(mockExportDataAsCsv).toHaveBeenCalledWith({
      ...csvOptions,
      utf8WithBom: true,
      fileName: expect.stringMatching(
        `^${csvOptions.fileName}_filtered_\\d{4}-\\d{2}-\\d{2}_\\d{2}-\\d{2}-\\d{2}$`
      ),
      getRowsToExport: exportForUnitTest.exportTypes.filtered,
    });

    // Check correct export function is called
    expect(gridExpandedSortedRowIdsSelector).toHaveBeenCalledTimes(1);

    // Menu should be closed after selection
    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
  });

  it('exports with default name if filename not set', async () => {
    // Mock no filename option
    csvOptions = {};

    // Render the component
    const component = render(<TestComponent />);

    // Click on the export button
    fireEvent.click(component.getByLabelText('Select export'));

    // Wait for the menu to appear
    await waitFor(() => screen.getByRole('menu'));

    // Click export all option
    fireEvent.click(component.getByText('Export all'));

    // Check that exportDataAsCsv was called with the correct options
    expect(mockExportDataAsCsv).toHaveBeenCalledTimes(1);
    expect(mockExportDataAsCsv).toHaveBeenCalledWith({
      utf8WithBom: true,
      fileName: expect.stringMatching(`^export_all_\\d{4}-\\d{2}-\\d{2}_\\d{2}-\\d{2}-\\d{2}$`),
      getRowsToExport: exportForUnitTest.exportTypes.all,
    });

    // Check correct export function is called
    expect(gridSortedRowIdsSelector).toHaveBeenCalledTimes(1);

    // Menu should be closed after selection
    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
  });

  it('handles enter event with keyboard navigation in the menu', async () => {
    const user = userEvent.setup();

    // Render the component
    const component = render(<TestComponent />);

    // Click on the export button
    fireEvent.click(component.getByLabelText('Select export'));

    // Wait for the menu to appear
    await waitFor(() => screen.getByRole('menu'));

    // Press down arrow to navigate to the next item
    await user.keyboard('{ArrowDown}');

    // Press Enter to select the item
    await user.keyboard('{Enter}');

    // Check that setDensity was called with the correct value (comfortable)
    expect(mockExportDataAsCsv).toHaveBeenCalledWith({
      ...csvOptions,
      utf8WithBom: true,
      fileName: expect.stringMatching(
        `^${csvOptions.fileName}_all_\\d{4}-\\d{2}-\\d{2}_\\d{2}-\\d{2}-\\d{2}$`
      ),
      getRowsToExport: exportForUnitTest.exportTypes.all,
    });

    // Check correct export function is called
    expect(gridSortedRowIdsSelector).toHaveBeenCalledTimes(1);

    // Menu should be closed after selection
    expect(component.queryByRole('menu')).not.toBeInTheDocument();
  });
});
